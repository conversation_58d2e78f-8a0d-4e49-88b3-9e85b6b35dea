"use client";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useInView } from "react-intersection-observer";

import Search from "@/components/icons/Search";
import PageHeader from "@/components/layout/pageHeader/PageHeader";
import AddTeamMember from "@/components/team/AddTeamMember";
import TeamCard from "@/components/team/TeamCard";
import { Input } from "@/components/ui/input";
import useGetTeamMembers from "@/services/teamMember/getTeamMembers";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import NoDataToShow from "@/components/ui/noDataToShow";
import withTeamGuard from "@/components/TeamGuard";
import EditIcon from "@/components/icons/Edit";
import Edit3 from "@/components/icons/Edit3";
import WorkingDaysModal from "@/components/team/WorkingDaysModal";
import useUpdateWorkingDays from "@/services/teamMember/updateWorkingDays";
import { WorkingDaysFormData } from "@/schema/workingDays";
import useGetTeamMembersCount from "@/services/teamMember/getTeamMembersCount";
import useGetWorkingDays from "@/services/teamMember/getWorkingDays";

const TeamPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchTerm = searchParams.get("search") || "";

  const { ref, inView } = useInView();

  const [search, setSearch] = useState(searchTerm);
  const [workingDaysModalOpen, setWorkingDaysModalOpen] = useState(false);

  const handleSearch = () => {
    if (!search && searchParams) {
      return router.push("/team");
    }

    router.push(`/team?search=${search}`);
  };

  const handleWorkingDaysClick = () => {
    setWorkingDaysModalOpen(true);
  };

  const {
    data,
    isPending,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetTeamMembers({ search: searchTerm });

  const { data: teamMembersCount } = useGetTeamMembersCount(); // Fetch total team members count
  const {
    data: workingDays,
    isSuccess: isWorkingDaysSuccess,
    refetch: refetchWorkingDays,
  } = useGetWorkingDays();
  const { mutate: updateWorkingDaysMutate, isPending: isUpdatingWorkingDays } =
    useUpdateWorkingDays(() => {
      setWorkingDaysModalOpen(false);
      refetchWorkingDays();
    });

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  if (isPending) return <Loader />;
  if (isError) return <ErrorText entity="team members" />;

  const teamMembers = data.pages.flatMap((page) => page.data) ?? [];

  const handleWorkingDaysSubmit = async (data: WorkingDaysFormData) => {
    updateWorkingDaysMutate(data.workingDays);
  };
  return (
    <div className="flex flex-col h-screen">
      <PageHeader>
        <div>
          <PageHeader.Heading>Manage your team</PageHeader.Heading>
          {teamMembersCount && (
            <PageHeader.Description>
              <span className="text-primary-blue-B900 font-semibold">
                {teamMembersCount}
              </span>
              <span className="text-neutrals-G400 ml-1">
                {teamMembersCount === 1 ? "member" : "members"}
              </span>
            </PageHeader.Description>
          )}
        </div>
        <AddTeamMember />
      </PageHeader>
      <div className="flex-1 overflow-auto p-5 scrollbar-hide">
        <div className="flex justify-between mb-6">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSearch();
            }}
            className="flex w-fit bg-neutrals-G40 rounded-lg overflow-hidden "
          >
            <Input
              type="search"
              placeholder="Search by name"
              className="w-1/4 min-w-[297px] h-[2.25rem] border-none bg-inherit placeholder:text-neutrals-G400 placeholder:text-sm placeholder:font-normal"
              value={search}
              onChange={(e) => setSearch(e.currentTarget.value)}
            />
            <button className="pr-3 py-2">
              <Search />
            </button>
          </form>
          <div className="rounded-lg flex justify-center items-center border border-[#2F80ED5E]">
            <div className="m-1 mx-3 flex items-center  gap-x-1">
              <span className="font-medium text-neutrals-G400 text-sm	">
                Working days:
              </span>
              <span className="text-nutrals-G900 font-bold	">{workingDays}</span>
              <button onClick={handleWorkingDaysClick}>
                <Edit3 />
              </button>
            </div>
          </div>
        </div>
        {teamMembers.length === 0 ? (
          <NoDataToShow />
        ) : (
          <>
            <div className="grid grid-cols-3 gap-4">
              {teamMembers.map((member) => (
                <TeamCard key={member._id} data={member} />
              ))}
            </div>
            <div
              ref={ref}
              className="w-full h-20 flex items-center justify-center"
            >
              {isFetchingNextPage && <Loader size={10} />}
            </div>
          </>
        )}
      </div>

      {/* Working Days Modal */}
      {isWorkingDaysSuccess && (
        <WorkingDaysModal
          open={workingDaysModalOpen}
          onOpenChange={setWorkingDaysModalOpen}
          initialValue={workingDays}
          onSubmit={handleWorkingDaysSubmit}
          loading={isUpdatingWorkingDays}
        />
      )}
    </div>
  );
};

export default withTeamGuard(TeamPage);
