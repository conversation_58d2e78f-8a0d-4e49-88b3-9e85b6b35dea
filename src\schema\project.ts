import { checkIsStartDateLowerThanOrEqualsToEndDate } from "@/lib/utils";
import { z } from "zod";

export const prosjectSchema = z
  .object({
    projectDetails: z.object({
      name: z.string().min(1, { message: "" }),
      type: z.string().optional(),
      contractorId: z.string().min(1, { message: "" }),
    }),
    clientDetails: z.object({
      name: z.string().min(1, { message: "" }),
      whatsappNumber: z.string().min(10, { message: "" }).max(10, ""),
      location: z.string().min(1, { message: "" }),
    }),
    startDate: z.date({ required_error: "" }),
    endDate: z.date({ required_error: "" }),
    duration: z.number().min(1).optional(),
  })
  .refine(
    (data) => {
      return checkIsStartDateLowerThanOrEqualsToEndDate(
        new Date(data.startDate),
        new Date(data.endDate),
      );
    },
    {
      message: "End date cannot be lower than start date",
      path: ["endDate"],
    },
  );

export const projectSchema = z
  .object({
    name: z.string().min(1, { message: "Project name is required" }),
    startDate: z.date().optional(),
    endDate: z.date().optional(),
    duration: z.number().min(1).optional(),
    projectType: z.string().optional(),
    numberOfFloors: z.coerce.number().optional(),
    expectedRevenue: z.coerce.number().optional(),
    requiredMargin: z.coerce.number().min(1).optional(),
    projectScope: z.enum(["design", "construction", "both"]).optional(),
    designStartDate: z.date().optional(),
    designEndDate: z.date().optional(),
    designDuration: z.number().optional(),
    contractorOrg: z.string().optional(),
    contractorStartDate: z.date().optional(),
    contractorEndDate: z.date().optional(),
    contractorDuration: z.number().optional(),
    clientName: z.string().optional(),
    clientEmail: z.string().email().optional(),
    clientWhatsAppNo: z.string().optional(),
    location: z.string().optional(),
  })
  .refine(
    (data) => {
      const { startDate, endDate } = data;

      if (!startDate || !endDate) return true;

      return checkIsStartDateLowerThanOrEqualsToEndDate(
        new Date(startDate),
        new Date(endDate),
      );
    },
    {
      message: "End date cannot be lower than start date",
      path: ["endDate"],
    },
  )
  .refine(
    (data) => {
      const { designStartDate, designEndDate } = data;

      if (!designStartDate || !designEndDate) return true;

      return checkIsStartDateLowerThanOrEqualsToEndDate(
        new Date(designStartDate),
        new Date(designEndDate),
      );
    },
    {
      message: "End date cannot be lower than start date",
      path: ["designEndDate"],
    },
  )
  .refine(
    (data) => {
      const { contractorStartDate, contractorEndDate } = data;

      if (!contractorStartDate || !contractorEndDate) return true;

      return checkIsStartDateLowerThanOrEqualsToEndDate(
        new Date(contractorStartDate),
        new Date(contractorEndDate),
      );
    },
    {
      message: "End date cannot be lower than start date",
      path: ["contractorEndDate"],
    },
  );
