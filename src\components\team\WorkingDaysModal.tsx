"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { X } from "lucide-react";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { workingDaysSchema, WorkingDaysFormData } from "@/schema/workingDays";

type WorkingDaysModalProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialValue?: number;
  onSubmit: (data: WorkingDaysFormData) => Promise<void>;
  loading: boolean;
};

function WorkingDaysModal({
  open,
  onOpenChange,
  initialValue,
  loading,
  onSubmit,
}: WorkingDaysModalProps) {
  const form = useForm<WorkingDaysFormData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(workingDaysSchema),
    defaultValues: {
      workingDays: initialValue,
    },
  });

  // Update form when initialValue changes
  useEffect(() => {
    form.reset({ workingDays: initialValue });
  }, [initialValue, form]);

  const handleSubmit = async (data: WorkingDaysFormData) => {
    try {
      await onSubmit(data);

      // Reset form and close modal on success
      form.reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Error submitting form:", error);
      // Error handling is done in the parent component's onSubmit
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogTitle className="flex items-center justify-between">
          Update the no of working days
          <DialogClose asChild>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="h-8 w-8 p-0 hover:bg-transparent"
            >
              <X className="h-6 w-6" />
            </Button>
          </DialogClose>
        </DialogTitle>

        <div className="text-sm text-neutrals-G600 mb-4">
          This value will be used to calculate salaries and adjust the overall
          project budget based on team working hours.
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <FormField
              control={form.control}
              name="workingDays"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-neutrals-G600">
                    Days
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      placeholder="Enter working days"
                      className="w-full"
                      disabled={loading}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button
                type="submit"
                loading={loading}
                disabled={loading}
                className="px-6"
              >
                Save changes
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export default WorkingDaysModal;
