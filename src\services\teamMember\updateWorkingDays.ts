import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import api from "@/lib/api-client";

const updateWorkingDays = async (workingDays: number) => {
  const { data } = await api({
    url: `/architect-team/working-days`,
    method: "PATCH",
    data: { workingDays },
  });
  return data;
};

const useUpdateWorkingDays = (onSuccess?: () => void) => {
  return useMutation({
    mutationFn: updateWorkingDays,
    onSuccess: () => {
      onSuccess?.();
      toast.success("Working days updated successfully");
    },
    onError: () => {
      toast.error("Failed to update working days");
    },
  });
};

export default useUpdateWorkingDays;
