import api from "@/lib/api-client";
import { useQuery } from "@tanstack/react-query";

const getTeamMembersCount = async () => {
  const { data } = await api.get(`/architect-team/members/count`);
  return data;
};

const useGetTeamMembersCount = () => {
  return useQuery({
    queryKey: ["architect-team-members-count"],
    queryFn: getTeamMembersCount,
  });
};

export default useGetTeamMembersCount;
